# Technology Stack

## Framework & Runtime
- **Nuxt 3** (v3.17.5) - Vue.js meta-framework with SSR enabled
- **Vue 3** (v3.5.14) - Frontend framework with Composition API
- **TypeScript** - Type-safe development
- **Bun** - Package manager and runtime (bun.lock present)

## UI & Styling
- **@finema/core** (v2.21.1) - Primary UI component library
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide Icons** - Icon system via @iconify-json/lucide
- Custom UI configuration in app.config.ts with Thai year support

## Development Tools
- **ESLint** - Code linting with strict formatting rules
- **Husky** - Git hooks for code quality
- **Vitest** - Testing framework
- **lint-staged** - Pre-commit linting

## Key Dependencies
- **Cheerio** - Server-side HTML parsing
- **Vue Router** - Client-side routing

## Common Commands

### Development
```bash
bun dev          # Start development server with auto-open
bun build        # Build for production
bun generate     # Generate static site
bun preview      # Preview production build
```

### Code Quality
```bash
bun lint         # Run ESLint
bun lint:fix     # Fix ESLint issues
bun test         # Run tests
bun test:watch   # Run tests in watch mode
bun test:coverage # Run tests with coverage
```

## Configuration Notes
- SSR enabled for better SEO and performance
- Thai language optimized with custom meta tags
- PWA-ready with service worker registration
- Custom UI theme with teal primary color (#14B8A6)
