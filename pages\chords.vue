<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="border-b bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <NuxtLink to="/" class="text-2xl font-bold text-gray-900">
              Mhalong chords
            </NuxtLink>
          </div>
          <nav class="hidden space-x-8 md:flex">
            <NuxtLink to="/" class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600">
              หน้าแรก
            </NuxtLink>
            <NuxtLink to="/search" class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600">
              ค้นหาคอร์ด
            </NuxtLink>
            <NuxtLink to="/chords" class="rounded-md px-3 py-2 text-sm font-medium text-blue-600">
              คอร์ดทั้งหมด
            </NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <!-- Page Header -->
      <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
        <h1 class="mb-2 text-3xl font-bold text-gray-900">
          คอร์ดทั้งหมด
        </h1>
        <p class="text-gray-600">
          รวมคอร์ดกีตาร์ทุกประเภท เรียงตามความนิยม
        </p>
      </div>

      <!-- Filters -->
      <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
        <h2 class="mb-4 text-lg font-semibold text-gray-900">
          กรองตามประเภท
        </h2>

        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
          <!-- Difficulty Filter -->
          <div>
            <label class="mb-2 block text-sm font-medium text-gray-700">ระดับความยาก</label>
            <select
              v-model="filters.difficulty"
              @change="applyFilters"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              <option value="">
                ทั้งหมด
              </option>
              <option value="beginner">
                ง่าย
              </option>
              <option value="intermediate">
                ปานกลาง
              </option>
              <option value="advanced">
                ยาก
              </option>
            </select>
          </div>

          <!-- Type Filter -->
          <div>
            <label class="mb-2 block text-sm font-medium text-gray-700">ประเภทคอร์ด</label>
            <select
              v-model="filters.type"
              @change="applyFilters"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              <option value="">
                ทั้งหมด
              </option>
              <option value="major">
                เมเจอร์
              </option>
              <option value="minor">
                ไมเนอร์
              </option>
            </select>
          </div>

          <!-- Sort Filter -->
          <div>
            <label class="mb-2 block text-sm font-medium text-gray-700">เรียงตาม</label>
            <select
              v-model="filters.sort"
              @change="applyFilters"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              <option value="name">
                ชื่อคอร์ด
              </option>
              <option value="difficulty">
                ความยาก
              </option>
              <option value="type">
                ประเภท
              </option>
            </select>
          </div>
        </div>

        <div class="mt-4 flex items-center justify-between">
          <button
            @click="clearFilters"
            class="text-sm text-gray-600 hover:text-gray-800"
          >
            ล้างตัวกรอง
          </button>

          <p v-if="chords?.pagination" class="text-sm text-gray-600">
            พบ {{ chords.pagination.totalCount }} คอร์ด
          </p>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"/>
      </div>

      <!-- Chords Grid -->
      <div v-else-if="chords?.data?.length" class="rounded-lg bg-white p-6 shadow-sm">
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          <ChordCard
            v-for="chord in chords.data"
            :key="chord.id"
            :chord="chord"
            @click="viewChord(chord.id)"
          />
        </div>

        <!-- Pagination -->
        <div v-if="chords.pagination && chords.pagination.totalPages > 1" class="mt-8">
          <nav class="flex justify-center">
            <div class="flex space-x-2">
              <button
                @click="changePage(chords.pagination.currentPage - 1)"
                :disabled="!chords.pagination.hasPrev"
                class="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
              >
                ก่อนหน้า
              </button>

              <span class="px-3 py-2 text-sm font-medium text-gray-700">
                หน้า {{ chords.pagination.currentPage }} จาก {{ chords.pagination.totalPages }}
              </span>

              <button
                @click="changePage(chords.pagination.currentPage + 1)"
                :disabled="!chords.pagination.hasNext"
                class="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
              >
                ถัดไป
              </button>
            </div>
          </nav>
        </div>
      </div>

      <!-- No Results -->
      <div v-else class="rounded-lg bg-white p-12 text-center shadow-sm">
        <Icon name="lucide:music-off" class="mx-auto mb-4 h-16 w-16 text-gray-400" />
        <h3 class="mb-2 text-lg font-medium text-gray-900">
          ไม่พบคอร์ด
        </h3>
        <p class="text-gray-600">
          ลองปรับตัวกรองหรือลองใหม่อีกครั้ง
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO
useHead({
  title: 'คอร์ดทั้งหมด - Mhalong chords',
  meta: [
    {
      name: 'description',
      content: 'รวมคอร์ดกีตาร์ทุกประเภท เรียงตามความนิยม พร้อมตัวกรองและการค้นหา',
    },
  ],
})

// Reactive data
const loading = ref(true)
const chords = ref(null)
const currentPage = ref(1)

const filters = ref({
  difficulty: '',
  type: '',
  sort: 'name',
})

// Methods
const fetchChords = async () => {
  loading.value = true

  try {
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: '20',
    })

    // Add filters to params if they exist
    if (filters.value.difficulty) {
      params.append('difficulty', filters.value.difficulty)
    }

    if (filters.value.type) {
      params.append('type', filters.value.type)
    }

    if (filters.value.sort) {
      params.append('sort', filters.value.sort)
    }

    const response = await $fetch(`/api/chords?${params.toString()}`)

    chords.value = response
  } catch (error) {
    console.error('Error fetching chords:', error)
  } finally {
    loading.value = false
  }
}

const applyFilters = () => {
  currentPage.value = 1
  fetchChords()
}

const clearFilters = () => {
  filters.value = {
    difficulty: '',
    type: '',
    sort: 'name',
  }

  currentPage.value = 1
  fetchChords()
}

const changePage = (page: number) => {
  if (page < 1) return
  currentPage.value = page
  fetchChords()
}

const viewChord = (id: string) => {
  navigateTo(`/chord/${id}`)
}

// Initialize
onMounted(() => {
  fetchChords()
})
</script>
