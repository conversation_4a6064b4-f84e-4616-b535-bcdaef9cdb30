<template>
  <NuxtLink :to="`/song/${song.id}`">
    <Card
      class="hover:ring-primary-500 hover:ring-1 cursor-pointer"
      @click="$emit('click')"
    >
      <div class="flex items-start gap-2">
        <div>
          <Image
            class="w-12 rounded-md object-cover"
            :src="song.thumbnail"
            :alt="song.title"
          >
            <template #error>
              <div class="flex h-12 w-12 items-center justify-center rounded-md bg-white">
                <Icon
                  name="ph:music-notes"
                  class="size-8"
                />
              </div>
            </template>
          </Image>
        </div>
        <div class="flex-1">
          <h3 class="text-secondary font-bold">
            {{ song.title }}
          </h3>
          <p class="text-muted text-sm">
            {{ song.artist }}
          </p>
        </div>
      </div>
    </Card>
  </NuxtLink>
</template>

<script setup lang="ts">
import { Image } from '#components'

interface Song {
  id: string
  title: string
  artist: string
  genre: string
  genreThai: string
  difficulty: string
  difficultyThai: string
  key: string
  tempo: number
  timeSignature: string
  capo: number
  strummingPattern: string
  chords: string[]
  tags: string[]
  description: string
  youtubeId: string
  duration: string
  releaseYear: number
  album: string
  thumbnail?: string
}

interface Props {
  song: Song
}

const props = defineProps<Props>()

defineEmits<{
  click: []
}>()

// YouTube Player
const {
  playSong,
} = useYouTubePlayer()

// Methods
const playInPlayer = () => {
  if (props.song.youtubeId) {
    playSong({
      id: props.song.id,
      title: props.song.title,
      artist: props.song.artist,
      youtubeId: props.song.youtubeId,
      thumbnail: props.song.thumbnail,
    })
  }
}

// Computed
const difficultyColor = computed(() => {
  const difficulty = props.song.difficulty

  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-800'
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800'
    case 'advanced':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
