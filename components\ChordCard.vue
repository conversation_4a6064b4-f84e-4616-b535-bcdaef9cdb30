<template>
  <div
    class="cursor-pointer overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-shadow hover:shadow-lg"
    @click="$emit('click')"
  >
    <!-- Chord Header -->
    <div class="border-b border-gray-100 p-4">
      <div class="mb-2 flex items-start justify-between">
        <h3 class="text-2xl font-bold text-gray-900">
          {{ chord.name }}
        </h3>
        <span
          class="rounded-full px-2 py-1 text-xs font-medium"
          :class="difficultyColor"
        >
          {{ chord.difficultyThai }}
        </span>
      </div>
      <p class="text-sm text-gray-600">
        {{ chord.typeThai }}
      </p>
    </div>

    <!-- Chord Diagram -->
    <div class="p-4">
      <div class="mb-4">
        <ChordDiagram
          :fingering="chord.fingering"
          :chord-name="chord.name"
        />
      </div>

      <!-- Notes -->
      <div class="mb-3">
        <p class="mb-1 text-sm text-gray-600">
          โน้ต:
        </p>
        <div class="flex space-x-2">
          <span
            v-for="note in chord.notes"
            :key="note"
            class="rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
          >
            {{ note }}
          </span>
        </div>
      </div>

      <!-- Tags -->
      <div class="mb-3">
        <div class="flex flex-wrap gap-1">
          <span
            v-for="tag in chord.tags.slice(0, 3)"
            :key="tag"
            class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700"
          >
            {{ tag }}
          </span>
        </div>
      </div>

      <!-- Description -->
      <p class="line-clamp-2 text-sm text-gray-600">
        {{ chord.description }}
      </p>
    </div>

    <!-- Footer -->
    <div class="border-t border-gray-100 bg-gray-50 px-4 py-3">
      <div class="flex items-center justify-between">
        <span class="text-xs text-gray-500">
          {{ chord.relatedSongs?.length || 0 }} เพลงที่เกี่ยวข้อง
        </span>
        <Icon
          name="lucide:arrow-right"
          class="h-4 w-4 text-gray-400"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ChordFingering {
  frets: number[]
  fingers: number[]
  baseFret: number
}

interface Chord {
  id: string
  name: string
  type: string
  typeThai: string
  difficulty: string
  difficultyThai: string
  fingering: ChordFingering
  alternatives?: ChordFingering[]
  notes: string[]
  tags: string[]
  relatedSongs: string[]
  description: string
}

interface Props {
  chord: Chord
}

const props = defineProps<Props>()

defineEmits<{
  click: []
}>()

// Computed
const difficultyColor = computed(() => {
  const difficulty = props.chord.difficulty

  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-800'
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800'
    case 'advanced':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
