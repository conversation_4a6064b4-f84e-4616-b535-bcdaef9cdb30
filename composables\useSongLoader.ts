export interface SongSmallItem {
  id: string
  title: string
  artist: string
  thumbnail: string
}

export interface SongSection {
  title: string
  description: string
  dateRange: string
  songs: SongSmallItem[]
}

export const useSongSectionsLoader = () => {
  return useListLoader<SongSection>({
    url: '/api/songs',
  })
}

export const useSongSearchLoader = () => {
  return usePageLoader<SongSmallItem>({
    baseURL: '/api/songs/search',
  })
}
