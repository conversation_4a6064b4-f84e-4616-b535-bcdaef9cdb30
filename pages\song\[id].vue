<template>
  <!-- Loading State -->
  <Loader
    :loading="loading"
  >
    <div
      v-if="error"
    >
      <Icon
        name="lucide:alert-circle"
        class="mx-auto mb-4 h-16 w-16 text-red-500"
      />
      <h2 class="mb-2 text-2xl font-bold text-gray-900">
        ไม่พบเพลงที่ต้องการ
      </h2>
      <p class="mb-6 text-gray-600">
        {{ error }}
      </p>
      <NuxtLink
        to="/songs"
        class="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
      >
        <Icon
          name="lucide:music"
          class="mr-2 h-4 w-4"
        />
        ดูเพลงอื่น
      </NuxtLink>
    </div>

    <!-- Song Detail -->
    <div
      v-else-if="song"
    >
      <!-- Breadcrumb -->
      <nav class="mb-6">
        <ol class="flex items-center space-x-2 text-sm text-white">
          <li>
            <NuxtLink
              to="/"
              class="hover:text-secondary"
            >
              หน้าแรก
            </NuxtLink>
          </li>
          <li>
            <Icon
              name="lucide:chevron-right"
              class="h-4 w-4"
            />
          </li>
          <li>
            <NuxtLink
              to="/songs"
              class="hover:text-secondary"
            >
              เพลงและคอร์ด
            </NuxtLink>
          </li>
          <li>
            <Icon
              name="lucide:chevron-right"
              class="h-4 w-4"
            />
          </li>
          <li class="font-medium text-gray-900">
            {{ song.title }}
          </li>
        </ol>
      </nav>

      <SongLyrics :song="song" />
    </div>
    <YouTubePlayer />
  </Loader>

  <!-- Error State -->
</template>

<script setup lang="ts">
// Router
const route = useRoute()
const songId = route.params.id as string

// YouTube Player
const {
  playSong,
} = useYouTubePlayer()

// Reactive data
const loading = ref(true)
const error = ref('')
const song = ref<any>(null)

// Fetch song data
onMounted(async () => {
  try {
    const response = await $fetch(`/api/songs/${songId}`)

    song.value = response.data

    // Update SEO
    useHead({
      title: `${song.value.title} - ${song.value.artist} | Mhalong chords`,
      meta: [
        {
          name: 'description',
          content: `เนื้อเพลงและคอร์ดกีตาร์ ${song.value.title} โดย ${song.value.artist} - ${song.value.description}`,
        },
      ],
    })

    if (song.value && song.value.youtubeId) {
      playSong({
        id: song.value.id,
        title: song.value.title,
        artist: song.value.artist,
        youtubeId: song.value.youtubeId,
        thumbnail: song.value.thumbnail,
      })
    }
  } catch (err) {
    error.value = err.statusMessage || 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
  } finally {
    loading.value = false
  }
})
</script>
