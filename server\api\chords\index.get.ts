export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const page = Number.parseInt(query.page as string) || 1
  const limit = Number.parseInt(query.limit as string) || 20

  // Mock chord data ภาษาไทย
  const mockChords = [
    {
      id: '1',
      name: 'C',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [0, 1, 0, 2, 3, 0],
        fingers: [0, 1, 0, 2, 3, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [3, 3, 2, 0, 1, 0],
          fingers: [3, 4, 2, 0, 1, 0],
          baseFret: 1,
        },
      ],
      notes: ['C', 'E', 'G'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'เมเจอร์'],
      relatedSongs: ['Let It Be - The Beatles', 'Wonderwall - Oasis'],
      description: 'คอร์ด C เมเจอร์ เป็นคอร์ดพื้นฐานที่ง่ายที่สุดสำหรับผู้เริ่มต้น',
    },
    {
      id: '2',
      name: 'G',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [3, 2, 0, 0, 3, 3],
        fingers: [3, 1, 0, 0, 4, 4],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [3, 2, 0, 0, 0, 3],
          fingers: [3, 1, 0, 0, 0, 4],
          baseFret: 1,
        },
      ],
      notes: ['G', 'B', 'D'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'เมเจอร์'],
      relatedSongs: ['Good Riddance - Green Day', 'Wish You Were Here - Pink Floyd'],
      description: 'คอร์ด G เมเจอร์ เป็นคอร์ดที่ใช้บ่อยในเพลงป็อป',
    },
    {
      id: '3',
      name: 'Am',
      type: 'minor',
      difficulty: 'beginner',
      fingering: {
        frets: [0, 0, 2, 2, 1, 0],
        fingers: [0, 0, 2, 3, 1, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [5, 7, 7, 5, 5, 5],
          fingers: [1, 3, 4, 1, 1, 1],
          baseFret: 5,
        },
      ],
      notes: ['A', 'C', 'E'],
      tags: ['basic', 'open', 'minor'],
      relatedSongs: ['Stairway to Heaven - Led Zeppelin', 'House of the Rising Sun - The Animals'],
    },
    {
      id: '4',
      name: 'F',
      type: 'major',
      difficulty: 'intermediate',
      fingering: {
        frets: [1, 1, 3, 3, 2, 1],
        fingers: [1, 1, 4, 3, 2, 1],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [1, 3, 3, 2, 1, 1],
          fingers: [1, 3, 4, 2, 1, 1],
          baseFret: 1,
        },
      ],
      notes: ['F', 'A', 'C'],
      tags: ['barre', 'major', 'intermediate'],
      relatedSongs: ['Hey Jude - The Beatles', 'Blackbird - The Beatles'],
    },
    {
      id: '5',
      name: 'Em',
      type: 'minor',
      difficulty: 'beginner',
      fingering: {
        frets: [0, 2, 2, 0, 0, 0],
        fingers: [0, 2, 3, 0, 0, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [0, 2, 2, 0, 3, 0],
          fingers: [0, 1, 2, 0, 4, 0],
          baseFret: 1,
        },
      ],
      notes: ['E', 'G', 'B'],
      tags: ['basic', 'open', 'minor'],
      relatedSongs: ['Losing Religion - R.E.M.', 'Creep - Radiohead'],
    },
    {
      id: '6',
      name: 'D',
      type: 'major',
      difficulty: 'beginner',
      fingering: {
        frets: [-1, -1, 0, 2, 3, 2],
        fingers: [0, 0, 0, 1, 3, 2],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [2, 0, 0, 2, 3, 2],
          fingers: [1, 0, 0, 2, 4, 3],
          baseFret: 1,
        },
      ],
      notes: ['D', 'F#', 'A'],
      tags: ['basic', 'open', 'major'],
      relatedSongs: ['Wonderwall - Oasis', 'Free Fallin\' - Tom Petty'],
    },
    {
      id: '7',
      name: 'A',
      type: 'major',
      difficulty: 'beginner',
      fingering: {
        frets: [0, 0, 2, 2, 2, 0],
        fingers: [0, 0, 1, 2, 3, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [5, 7, 7, 6, 5, 5],
          fingers: [1, 3, 4, 2, 1, 1],
          baseFret: 5,
        },
      ],
      notes: ['A', 'C#', 'E'],
      tags: ['basic', 'open', 'major'],
      relatedSongs: ['Free Bird - Lynyrd Skynyrd', 'Layla - Eric Clapton'],
    },
    {
      id: '8',
      name: 'E',
      type: 'major',
      difficulty: 'beginner',
      fingering: {
        frets: [0, 2, 2, 1, 0, 0],
        fingers: [0, 2, 3, 1, 0, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [0, 2, 2, 1, 0, 0],
          fingers: [0, 3, 4, 1, 0, 0],
          baseFret: 1,
        },
      ],
      notes: ['E', 'G#', 'B'],
      tags: ['basic', 'open', 'major'],
      relatedSongs: ['Brown Eyed Girl - Van Morrison', 'Wild Thing - The Troggs'],
    },
  ]

  // Add more mock chords to simulate pagination
  const allChords = []

  for (let i = 0; i < 100; i++) {
    allChords.push(...mockChords.map((chord) => ({
      ...chord,
      id: `${chord.id}_${i}`,
      name: i === 0 ? chord.name : `${chord.name}${i > 0 ? ` (${i})` : ''}`,
    })))
  }

  // Pagination logic
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit
  const paginatedChords = allChords.slice(startIndex, endIndex)

  const totalCount = allChords.length
  const totalPages = Math.ceil(totalCount / limit)

  return {
    success: true,
    data: paginatedChords,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  }
})
