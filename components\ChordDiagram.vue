<template>
  <div class="chord-diagram">
    <div class="diagram-container">
      <!-- Fret Numbers -->
      <div class="fret-numbers">
        <span
          v-for="fret in visibleFrets"
          :key="fret"
          class="fret-number"
        >
          {{ fret }}
        </span>
      </div>

      <!-- Guitar Neck -->
      <div class="guitar-neck">
        <!-- Strings (vertical lines) -->
        <div
          v-for="string in 6"
          :key="`string-${string}`"
          class="guitar-string"
          :style="{ left: `${(string - 1) * 20}%` }"
        />

        <!-- Frets (horizontal lines) -->
        <div
          v-for="fret in visibleFrets.length"
          :key="`fret-${fret}`"
          class="guitar-fret"
          :style="{ top: `${(fret - 1) * 25}%` }"
        />

        <!-- Finger positions -->
        <div
          v-for="(fret, stringIndex) in fingering.frets"
          :key="`finger-${stringIndex}`"
          class="finger-position"
          :class="{
            muted: fret === -1,
            open: fret === 0,
            pressed: fret > 0,
          }"
          :style="getFingerStyle(stringIndex, fret)"
        >
          <span
            v-if="fret > 0"
            class="finger-number"
          >
            {{ fingering.fingers[stringIndex] || '' }}
          </span>
          <span
            v-else-if="fret === 0"
            class="open-string"
          >○</span>
          <span
            v-else
            class="muted-string"
          >×</span>
        </div>
      </div>

      <!-- String labels -->
      <div class="string-labels">
        <span
          v-for="(note, index) in stringNotes"
          :key="`note-${index}`"
          class="string-label"
        >
          {{ note }}
        </span>
      </div>
    </div>

    <!-- Chord info -->
    <div class="chord-info">
      <p class="chord-name">
        {{ chordName }}
      </p>
      <p
        v-if="fingering.baseFret > 1"
        class="base-fret"
      >
        เฟรต {{ fingering.baseFret }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ChordFingering {
  frets: number[]
  fingers: number[]
  baseFret: number
}

interface Props {
  fingering: ChordFingering
  chordName: string
}

const props = defineProps<Props>()

// Standard guitar tuning (from 6th string to 1st string)
const standardTuning = ['E', 'A', 'D', 'G', 'B', 'E']

// Computed
const visibleFrets = computed(() => {
  const baseFret = props.fingering.baseFret
  const maxFret = Math.max(...props.fingering.frets.filter((f) => f > 0))
  const fretRange = Math.max(4, maxFret - baseFret + 2)

  return Array.from({
    length: fretRange,
  }, (_, i) => baseFret + i)
})

const stringNotes = computed(() => {
  return standardTuning.reverse() // Display from 1st string to 6th string
})

// Methods
const getFingerStyle = (stringIndex: number, fret: number) => {
  const stringPosition = stringIndex * 20 // 20% per string

  if (fret === -1 || fret === 0) {
    // Muted or open string - position above the neck
    return {
      left: `${stringPosition}%`,
      top: '-15px',
      transform: 'translateX(-50%)',
    }
  }

  // Pressed fret - position on the neck
  const fretPosition = (fret - props.fingering.baseFret + 0.5) * 25 // 25% per fret

  return {
    left: `${stringPosition}%`,
    top: `${fretPosition}%`,
    transform: 'translateX(-50%) translateY(-50%)',
  }
}
</script>

<style scoped>
@import "tailwindcss";

.chord-diagram {
  @apply w-full max-w-xs mx-auto;
}

.diagram-container {
  @apply relative bg-amber-50 rounded-lg p-4;
  min-height: 200px;
}

.fret-numbers {
  @apply flex justify-between text-xs text-gray-600 mb-2;
  margin-left: 10%;
  margin-right: 10%;
}

.fret-number {
  @apply font-medium;
}

.guitar-neck {
  @apply relative bg-amber-100 rounded border-2 border-amber-200;
  height: 150px;
  margin: 0 10%;
}

.guitar-string {
  @apply absolute bg-gray-800;
  width: 1px;
  height: 100%;
  transform: translateX(-50%);
}

.guitar-fret {
  @apply absolute bg-gray-600;
  width: 100%;
  height: 1px;
}

.finger-position {
  @apply absolute flex items-center justify-center text-xs font-bold;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.finger-position.pressed {
  @apply bg-blue-600 text-white border-2 border-blue-700;
}

.finger-position.open {
  @apply bg-white text-gray-800 border-2 border-gray-400;
}

.finger-position.muted {
  @apply bg-red-500 text-white;
}

.finger-number {
  @apply text-xs font-bold;
}

.open-string {
  @apply text-lg;
}

.muted-string {
  @apply text-lg font-bold;
}

.string-labels {
  @apply flex justify-between text-xs text-gray-600 mt-2;
  margin-left: 10%;
  margin-right: 10%;
}

.string-label {
  @apply font-medium;
}

.chord-info {
  @apply text-center mt-3;
}

.chord-name {
  @apply text-lg font-bold text-gray-900;
}

.base-fret {
  @apply text-sm text-gray-600;
}
</style>
