export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')

  // Mock chord data สำหรับการดึงข้อมูลคอร์ดเฉพาะ
  const mockChords = [
    {
      id: '1',
      name: 'C',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [0, 1, 0, 2, 3, 0],
        fingers: [0, 1, 0, 2, 3, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [3, 3, 2, 0, 1, 0],
          fingers: [3, 4, 2, 0, 1, 0],
          baseFret: 1,
          description: 'รูปแบบบาร์เร่ที่เฟรต 3',
        },
        {
          frets: [8, 10, 10, 9, 8, 8],
          fingers: [1, 3, 4, 2, 1, 1],
          baseFret: 8,
          description: 'รูปแบบบาร์เร่ที่เฟรต 8',
        },
      ],
      notes: ['C', 'E', 'G'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'เมเจอร์'],
      relatedSongs: [
        'Let It Be - The Beatles',
        'Wonderwall - Oasis',
        'Hey Soul Sister - Train',
        'Someone Like You - Adele',
      ],
      description: 'คอร์ด C เมเจอร์ เป็นคอร์ดพื้นฐานที่ง่ายที่สุดสำหรับผู้เริ่มต้น เป็นคอร์ดแรกที่ผู้เรียนกีตาร์ส่วนใหญ่จะเรียนรู้',
      tips: [
        'ใช้ปลายนิ้วกดให้ตั้งฉาก',
        'อย่าให้นิ้วแตะเส้นอื่น',
        'ฝึกเปลี่ยนจาก C ไป G และกลับมา',
        'เริ่มต้นด้วยการดีดเส้นทีละเส้น',
      ],
      relatedChords: [
        {
          id: '2',
          name: 'G',
          relation: 'คู่กันบ่อย',
        },
        {
          id: '3',
          name: 'Am',
          relation: 'progression พื้นฐาน',
        },
        {
          id: '4',
          name: 'F',
          relation: 'progression พื้นฐาน',
        },
      ],
    },
    {
      id: '2',
      name: 'G',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [3, 2, 0, 0, 3, 3],
        fingers: [3, 1, 0, 0, 4, 4],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [3, 2, 0, 0, 0, 3],
          fingers: [3, 1, 0, 0, 0, 4],
          baseFret: 1,
          description: 'รูปแบบง่ายกว่า (ไม่กดเส้น 1)',
        },
        {
          frets: [3, 5, 5, 4, 3, 3],
          fingers: [1, 3, 4, 2, 1, 1],
          baseFret: 3,
          description: 'รูปแบบบาร์เร่ที่เฟรต 3',
        },
      ],
      notes: ['G', 'B', 'D'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'เมเจอร์'],
      relatedSongs: [
        'Good Riddance - Green Day',
        'Wish You Were Here - Pink Floyd',
        'Wonderwall - Oasis',
        'Horse with No Name - America',
      ],
      description: 'คอร์ด G เมเจอร์ เป็นคอร์ดที่ใช้บ่อยในเพลงป็อป ร็อค และคันทรี่ ให้เสียงที่เปิดและสดใส',
      tips: [
        'นิ้วกลางและนิ้วนางต้องโค้งให้ดี',
        'อย่าให้นิ้วแตะเส้น 4 และ 5',
        'ฝึกเปลี่ยนจาก G ไป C',
        'เริ่มต้นด้วยรูปแบบง่ายก่อน',
      ],
      relatedChords: [
        {
          id: '1',
          name: 'C',
          relation: 'คู่กันบ่อย',
        },
        {
          id: '6',
          name: 'D',
          relation: 'progression พื้นฐาน',
        },
        {
          id: '5',
          name: 'Em',
          relation: 'progression พื้นฐาน',
        },
      ],
    },
    {
      id: '3',
      name: 'Am',
      type: 'minor',
      typeThai: 'ไมเนอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [0, 0, 2, 2, 1, 0],
        fingers: [0, 0, 2, 3, 1, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [5, 7, 7, 5, 5, 5],
          fingers: [1, 3, 4, 1, 1, 1],
          baseFret: 5,
          description: 'รูปแบบบาร์เร่ที่เฟรต 5',
        },
      ],
      notes: ['A', 'C', 'E'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'ไมเนอร์'],
      relatedSongs: [
        'Stairway to Heaven - Led Zeppelin',
        'House of the Rising Sun - The Animals',
        'Mad World - Gary Jules',
        'Hurt - Johnny Cash',
      ],
      description: 'คอร์ด A ไมเนอร์ ให้เสียงที่เศร้าและอ่อนโยน เป็นคอร์ดไมเนอร์ที่ง่ายที่สุด',
      tips: [
        'นิ้วชี้กดเส้น 2 เฟรต 1',
        'นิ้วกลางและนิ้วนางกดเส้น 3 และ 4',
        'ฝึกเปลี่ยนจาก Am ไป C',
        'เป็นคอร์ดที่ดีสำหรับฝึกเปลี่ยนคอร์ด',
      ],
      relatedChords: [
        {
          id: '1',
          name: 'C',
          relation: 'progression พื้นฐาน',
        },
        {
          id: '4',
          name: 'F',
          relation: 'progression พื้นฐาน',
        },
        {
          id: '2',
          name: 'G',
          relation: 'progression พื้นฐาน',
        },
      ],
    },
  ]

  const chord = mockChords.find((c) => c.id === id)

  if (!chord) {
    throw createError({
      statusCode: 404,
      statusMessage: 'ไม่พบคอร์ดที่ต้องการ',
    })
  }

  return {
    success: true,
    data: chord,
  }
})
