<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="border-b bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <NuxtLink to="/" class="text-2xl font-bold text-gray-900">
              Mhalong chords
            </NuxtLink>
          </div>
          <nav class="hidden space-x-8 md:flex">
            <NuxtLink to="/" class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600">
              หน้าแรก
            </NuxtLink>
            <NuxtLink to="/search" class="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600">
              ค้นหาคอร์ด
            </NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-20">
      <div class="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"/>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="mx-auto max-w-7xl px-4 py-20 text-center sm:px-6 lg:px-8">
      <Icon name="lucide:alert-circle" class="mx-auto mb-4 h-16 w-16 text-red-500" />
      <h2 class="mb-2 text-2xl font-bold text-gray-900">
        ไม่พบคอร์ดที่ต้องการ
      </h2>
      <p class="mb-6 text-gray-600">
        {{ error }}
      </p>
      <NuxtLink
        to="/search"
        class="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
      >
        <Icon name="lucide:search" class="mr-2 h-4 w-4" />
        ค้นหาคอร์ดอื่น
      </NuxtLink>
    </div>

    <!-- Chord Detail -->
    <div v-else-if="chord" class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <!-- Breadcrumb -->
      <nav class="mb-6">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li><NuxtLink to="/" class="hover:text-blue-600">หน้าแรก</NuxtLink></li>
          <li><Icon name="lucide:chevron-right" class="h-4 w-4" /></li>
          <li><NuxtLink to="/search" class="hover:text-blue-600">ค้นหาคอร์ด</NuxtLink></li>
          <li><Icon name="lucide:chevron-right" class="h-4 w-4" /></li>
          <li class="font-medium text-gray-900">
            {{ chord.name }}
          </li>
        </ol>
      </nav>

      <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <!-- Main Content -->
        <div class="lg:col-span-2">
          <!-- Chord Header -->
          <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <div class="mb-4 flex items-start justify-between">
              <div>
                <h1 class="mb-2 text-4xl font-bold text-gray-900">
                  {{ chord.name }}
                </h1>
                <p class="text-lg text-gray-600">
                  {{ chord.typeThai }}
                </p>
              </div>
              <span
                class="rounded-full px-3 py-1 text-sm font-medium"
                :class="difficultyColor"
              >
                {{ chord.difficultyThai }}
              </span>
            </div>

            <p class="leading-relaxed text-gray-700">
              {{ chord.description }}
            </p>
          </div>

          <!-- Main Chord Diagram -->
          <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h2 class="mb-4 text-2xl font-bold text-gray-900">
              แผนภาพคอร์ด
            </h2>
            <div class="flex justify-center">
              <ChordDiagram :fingering="chord.fingering" :chord-name="chord.name" />
            </div>

            <!-- Notes -->
            <div class="mt-6">
              <h3 class="mb-2 text-lg font-semibold text-gray-900">
                โน้ตในคอร์ด
              </h3>
              <div class="flex space-x-3">
                <span
                  v-for="note in chord.notes"
                  :key="note"
                  class="rounded-lg bg-blue-100 px-4 py-2 font-medium text-blue-800"
                >
                  {{ note }}
                </span>
              </div>
            </div>
          </div>

          <!-- Alternative Fingerings -->
          <div v-if="chord.alternatives?.length" class="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h2 class="mb-4 text-2xl font-bold text-gray-900">
              รูปแบบอื่น
            </h2>
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div
                v-for="(alt, index) in chord.alternatives"
                :key="index"
                class="rounded-lg border border-gray-200 p-4"
              >
                <h3 class="mb-2 font-semibold text-gray-900">
                  รูปแบบที่ {{ index + 2 }}
                </h3>
                <p v-if="alt.description" class="mb-3 text-sm text-gray-600">
                  {{ alt.description }}
                </p>
                <ChordDiagram :fingering="alt" :chord-name="`${chord.name} (${index + 2})`" />
              </div>
            </div>
          </div>

          <!-- Tips -->
          <div v-if="chord.tips?.length" class="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h2 class="mb-4 text-2xl font-bold text-gray-900">
              เทคนิคการกด
            </h2>
            <ul class="space-y-2">
              <li
                v-for="tip in chord.tips"
                :key="tip"
                class="flex items-start"
              >
                <Icon name="lucide:lightbulb" class="mt-0.5 mr-2 h-5 w-5 flex-shrink-0 text-yellow-500" />
                <span class="text-gray-700">{{ tip }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Tags -->
          <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h3 class="mb-3 text-lg font-semibold text-gray-900">
              แท็ก
            </h3>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="tag in chord.tags"
                :key="tag"
                class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700"
              >
                {{ tag }}
              </span>
            </div>
          </div>

          <!-- Related Songs -->
          <div v-if="chord.relatedSongs?.length" class="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h3 class="mb-3 text-lg font-semibold text-gray-900">
              เพลงที่ใช้คอร์ดนี้
            </h3>
            <ul class="space-y-2">
              <li
                v-for="song in chord.relatedSongs"
                :key="song"
                class="flex items-center text-gray-700"
              >
                <Icon name="lucide:music" class="mr-2 h-4 w-4 text-gray-400" />
                <span class="text-sm">{{ song }}</span>
              </li>
            </ul>
          </div>

          <!-- Related Chords -->
          <div v-if="chord.relatedChords?.length" class="rounded-lg bg-white p-6 shadow-sm">
            <h3 class="mb-3 text-lg font-semibold text-gray-900">
              คอร์ดที่เกี่ยวข้อง
            </h3>
            <div class="space-y-2">
              <NuxtLink
                v-for="relatedChord in chord.relatedChords"
                :key="relatedChord.id"
                :to="`/chord/${relatedChord.id}`"
                class="flex items-center justify-between rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div>
                  <span class="font-medium text-gray-900">{{ relatedChord.name }}</span>
                  <p class="text-sm text-gray-600">{{ relatedChord.relation }}</p>
                </div>
                <Icon name="lucide:arrow-right" class="h-4 w-4 text-gray-400" />
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Router
const route = useRoute()
const chordId = route.params.id as string

// Reactive data
const loading = ref(true)
const error = ref('')
const chord = ref(null)

// Computed
const difficultyColor = computed(() => {
  if (!chord.value) return 'bg-gray-100 text-gray-800'

  const difficulty = chord.value.difficulty

  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-800'
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800'
    case 'advanced':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})

// Fetch chord data
onMounted(async () => {
  try {
    const response = await $fetch(`/api/chords/${chordId}`)

    chord.value = response.data

    // Update SEO
    useHead({
      title: `คอร์ด ${chord.value.name} ${chord.value.typeThai} - Mhalong chords`,
      meta: [
        {
          name: 'description',
          content: chord.value.description,
        },
      ],
    })
  } catch (err) {
    error.value = err.statusMessage || 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
  } finally {
    loading.value = false
  }
})
</script>
