export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const name = query.name as string

  if (!name) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing name parameter',
    })
  }

  try {
    // เรียก API จาก dochord.com สำหรับค้นหาเพลงตามชื่อ
    const dochordUrl = `https://www.dochord.com/wp-json/wp/v2/posts?search=${encodeURIComponent(name)}&_embed&per_page=1`

    const response = await $fetch(dochordUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    }) as any[]

    if (!response || response.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: 'ไม่พบเพลงที่ต้องการ',
      })
    }

    const post = response[0]
    const content = post.content?.rendered || ''
    const title = post.title?.rendered || ''
    const excerpt = post.excerpt?.rendered || ''

    // ดึงข้อมูลศิลปินจาก _embedded.wp:term
    const artistInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
      termGroup.some((term: any) => term.taxonomy === 'artist'),
    )?.find((term: any) => term.taxonomy === 'artist')

    // ดึงรูปภาพจาก _embedded.wp:featuredmedia
    const featuredImage = post._embedded?.['wp:featuredmedia']?.[0]?.source_url || ''

    // ดึงหมวดหมู่จาก _embedded.wp:term
    const categoryInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
      termGroup.some((term: any) => term.taxonomy === 'category'),
    )?.find((term: any) => term.taxonomy === 'category')

    // ดึงข้อมูล copyright จาก _embedded.wp:term
    const copyrightInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
      termGroup.some((term: any) => term.taxonomy === 'copyright'),
    )?.find((term: any) => term.taxonomy === 'copyright')

    const transformedSong = {
      id: post.id.toString(),
      title: title.replace(/<[^>]*>/g, ''),
      artist: artistInfo?.name || 'Unknown Artist',
      genre: categoryInfo?.name || 'Pop',
      genreThai: categoryInfo?.name || 'ป็อป',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      key: extractKeyFromContent(content) || 'C',
      tempo: 120,
      timeSignature: '4/4',
      capo: extractCapoFromContent(content) || 0,
      strummingPattern: 'D-D-U-D-U',
      chords: extractChordsFromContent(content),
      lyrics: extractLyricsFromContent(content),
      tags: ['dochord', 'เพลงไทย'],
      description: excerpt.replace(/<[^>]*>/g, '').substring(0, 200) + '...',
      youtubeId: extractYouTubeId(content) || '',
      duration: '3:30',
      releaseYear: new Date(post.date).getFullYear(),
      album: 'Single',
      url: post.link,
      image: featuredImage,
      content: content,
      slug: post.slug,
      date: post.date,
      copyright: copyrightInfo?.name || '',
      tips: extractTipsFromContent(content),
      relatedSongs: [],
    }

    return {
      success: true,
      data: transformedSong,
    }
  } catch (error) {
    console.error('Error finding song from dochord.com:', error)

    throw createError({
      statusCode: 404,
      statusMessage: 'ไม่พบเพลงที่ต้องการ',
    })
  }
})

function extractKeyFromContent(content: string): string {
  const keyPatterns = [
    /คีย์\s*(?::\s*)?([A-G][#b]?m?)/i,
    /key\s*(?::\s*)?([A-G][#b]?m?)/i,
    /ทำนอง\s*([A-G][#b]?m?)/i,
  ]

  for (const pattern of keyPatterns) {
    const match = content.match(pattern)

    if (match) {
      return match[1]
    }
  }

  return 'C'
}

function extractCapoFromContent(content: string): number {
  const capoPatterns = [
    /capo\s*(?::\s*)?(\d+)/i,
    /คาโป\s*(?::\s*)?(\d+)/,
    /เฟรต\s*(\d+)/,
  ]

  for (const pattern of capoPatterns) {
    const match = content.match(pattern)

    if (match) {
      return Number.parseInt(match[1])
    }
  }

  return 0
}

function extractChordsFromContent(content: string): string[] {
  const chordPattern = /\b([A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*)\b/g
  const matches = content.match(chordPattern) || []

  const uniqueChords = [...new Set(matches)]
    .filter((chord) => /^[A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*$/.test(chord))
    .slice(0, 10)

  return uniqueChords.length > 0 ? uniqueChords : ['C', 'G', 'Am', 'F']
}

function extractYouTubeId(content: string): string {
  const youtubePatterns = [
    /youtube\.com\/watch\?v=([\w-]+)/,
    /youtu\.be\/([\w-]+)/,
    /youtube\.com\/embed\/([\w-]+)/,
  ]

  for (const pattern of youtubePatterns) {
    const match = content.match(pattern)

    if (match) {
      return match[1]
    }
  }

  return ''
}

function extractLyricsFromContent(content: string): any[] {
  // ลบ HTML tags และแปลง HTML entities
  const cleanContent = content
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .trim()

  if (!cleanContent) {
    return []
  }

  const sections: any[] = []
  const lines = cleanContent.split('\n').filter((line) => line.trim())

  let currentSection = 'Verse'
  let currentLines: any[] = []
  let sectionCount = 1

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    // ข้าม intro และ instru
    if (line.match(/^(INTRO|INSTRU|intro|instru)\s*:/i)) {
      continue
    }

    // ตรวจสอบว่าเป็นส่วนใหม่หรือไม่ (เช่น *, chorus, bridge)
    if (line.match(/^\*+\s*$/) || line.match(/^(chorus|bridge|outro)/i)) {
      // บันทึกส่วนปัจจุบัน
      if (currentLines.length > 0) {
        sections.push({
          section: currentSection + (sectionCount > 1 ? ` ${sectionCount}` : ''),
          lines: currentLines,
        })

        currentLines = []
      }

      // เริ่มส่วนใหม่
      if (line.match(/^\*+\s*$/)) {
        currentSection = 'Chorus'
        sectionCount = 1
      } else {
        currentSection = line.charAt(0).toUpperCase() + line.slice(1).toLowerCase()
        sectionCount = 1
      }

      continue
    }

    // แยกคอร์ดและเนื้อเพลง
    const chordMatches = line.match(/[A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*/g) || []

    // ลบคอร์ดออกจากเนื้อเพลง
    let lyricsText = line

    chordMatches.forEach((chord) => {
      lyricsText = lyricsText.replace(new RegExp(chord, 'g'), '')
    })

    // ทำความสะอาดเนื้อเพลง
    lyricsText = lyricsText
      .replace(/\s+/g, ' ')
      .replace(/\|/g, '')
      .trim()

    if (lyricsText || chordMatches.length > 0) {
      currentLines.push({
        text: lyricsText || '',
        chords: [...new Set(chordMatches)], // ลบคอร์ดที่ซ้ำ
      })
    }
  }

  // บันทึกส่วนสุดท้าย
  if (currentLines.length > 0) {
    sections.push({
      section: currentSection + (sectionCount > 1 ? ` ${sectionCount}` : ''),
      lines: currentLines,
    })
  }

  return sections.length > 0
    ? sections
    : [
      {
        section: 'Verse 1',
        lines: [
          {
            text: 'ไม่สามารถแยกเนื้อเพลงได้',
            chords: [],
          },
        ],
      },
    ]
}

function extractTipsFromContent(content: string): string[] {
  return [
    'เทคนิคการเล่นจะถูกแยกจาก content ของ dochord.com',
  ]
}

function extractFromWebContent(webContent: string): { lyrics: any[]
  chords: string[]
  key: string } {
  try {
    // หาส่วนของเนื้อเพลงและคอร์ดจาก div.entry-content.single-entry-content
    let lyricsMatch = webContent.match(/<div[^>]*class="[^"]*entry-content[^"]*single-entry-content[^"]*"[^>]*>([\s\S]*?)<\/div>/i)

    // ถ้าไม่เจอ ลองหารูปแบบอื่น
    if (!lyricsMatch) {
      lyricsMatch = webContent.match(/<div[^>]*class="[^"]*entry-content[^"]*"[^>]*>([\s\S]*?)<\/div>/i)
    }

    // ลองหาจาก single-entry-content อย่างเดียว
    if (!lyricsMatch) {
      lyricsMatch = webContent.match(/<div[^>]*class="[^"]*single-entry-content[^"]*"[^>]*>([\s\S]*?)<\/div>/i)
    }

    // ลองหาจาก pattern ที่กว้างขึ้น
    if (!lyricsMatch) {
      lyricsMatch = webContent.match(/<div[^>]*entry-content[^>]*>([\s\S]*?)<\/div>/i)
    }

    if (!lyricsMatch) {
      return {
        lyrics: [],
        chords: [],
        key: 'C',
      }
    }

    const lyricsHtml = lyricsMatch[1]

    // ลบ HTML tags และแปลง HTML entities
    const cleanContent = lyricsHtml
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<p[^>]*>/gi, '\n')
      .replace(/<\/p>/gi, '\n')
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim()

    // แยกเนื้อเพลงเป็นส่วนๆ
    const sections: any[] = []
    const lines = cleanContent.split('\n').filter((line) => line.trim())

    let currentSection = 'Verse'
    let currentLines: any[] = []
    let sectionCount = 1
    const allChords = new Set<string>()
    let detectedKey = 'C'

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // ข้าม intro และ instru
      if (line.match(/^(INTRO|INSTRU|intro|instru)\s*:/i)) {
        continue
      }

      // หาคีย์จากบรรทัด
      const keyMatch = line.match(/Key\s+([A-G][#b]?)/i)

      if (keyMatch) {
        detectedKey = keyMatch[1]
        continue
      }

      // ตรวจสอบว่าเป็นส่วนใหม่หรือไม่
      if (line.match(/^\*+\s*$/) || line.match(/^(chorus|bridge|outro)/i)) {
        if (currentLines.length > 0) {
          sections.push({
            section: currentSection + (sectionCount > 1 ? ` ${sectionCount}` : ''),
            lines: currentLines,
          })

          currentLines = []
        }

        if (line.match(/^\*+\s*$/)) {
          currentSection = 'Chorus'
          sectionCount = 1
        } else {
          currentSection = line.charAt(0).toUpperCase() + line.slice(1).toLowerCase()
          sectionCount = 1
        }

        continue
      }

      // แยกคอร์ดและเนื้อเพลง
      const chordMatches = line.match(/[A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*/g) || []

      chordMatches.forEach((chord) => allChords.add(chord))

      // ลบคอร์ดออกจากเนื้อเพลง
      let lyricsText = line

      chordMatches.forEach((chord) => {
        lyricsText = lyricsText.replace(new RegExp(chord, 'g'), '')
      })

      lyricsText = lyricsText
        .replace(/\s+/g, ' ')
        .replace(/\|/g, '')
        .trim()

      if (lyricsText || chordMatches.length > 0) {
        currentLines.push({
          text: lyricsText || '',
          chords: [...new Set(chordMatches)],
        })
      }
    }

    // บันทึกส่วนสุดท้าย
    if (currentLines.length > 0) {
      sections.push({
        section: currentSection + (sectionCount > 1 ? ` ${sectionCount}` : ''),
        lines: currentLines,
      })
    }

    return {
      lyrics: sections,
      chords: Array.from(allChords),
      key: detectedKey,
    }
  } catch (error) {
    console.error('Error extracting from web content:', error)

    return {
      lyrics: [],
      chords: [],
      key: 'C',
    }
  }
}
