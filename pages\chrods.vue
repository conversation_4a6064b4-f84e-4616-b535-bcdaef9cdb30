<template>
  <!-- Search Section -->
  <div class="mb-8 rounded-lg bg-white/80 p-6 shadow-sm">
    <h1 class="mb-6 text-3xl font-bold text-gray-900">
      ค้นหาคอร์ด
    </h1>

    <div class="flex flex-col gap-4 md:flex-row">
      <div class="flex-1">
        <div class="relative">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="ค้นหาคอร์ด เช่น C, Am, G, เมเจอร์, ง่าย..."
            class="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 pr-4 pl-12 text-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none"
            @keyup.enter="performSearch"
          />
          <div class="absolute inset-y-0 left-0 flex items-center pl-4">
            <Icon
              name="lucide:search"
              class="h-5 w-5 text-gray-400"
            />
          </div>
        </div>
      </div>

      <button
        :disabled="loading"
        class="rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
        @click="performSearch"
      >
        <Icon
          v-if="loading"
          name="lucide:loader-2"
          class="h-5 w-5 animate-spin"
        />
        <span v-else>ค้นหา</span>
      </button>
    </div>

    <!-- Quick filters -->
    <div class="mt-4">
      <p class="mb-2 text-sm text-gray-600">
        ค้นหาด่วน:
      </p>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="filter in quickFilters"
          :key="filter"
          class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700 transition-colors hover:bg-gray-200"
          @click="quickSearch(filter)"
        >
          {{ filter }}
        </button>
      </div>
    </div>
  </div>

  <!-- Results Section -->
  <div class="rounded-lg bg-white p-6 shadow-sm">
    <!-- Results Header -->
    <div class="mb-6 flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">
          <span v-if="currentQuery">ผลการค้นหา "{{ currentQuery }}"</span>
          <span v-else>คอร์ดทั้งหมด</span>
        </h2>
        <p
          v-if="searchResults?.pagination"
          class="mt-1 text-sm text-gray-600"
        >
          พบ {{ searchResults.pagination.totalCount }} คอร์ด
          (หน้า {{ searchResults.pagination.currentPage }} จาก {{ searchResults.pagination.totalPages }})
        </p>
      </div>
    </div>

    <SongListSkeleton v-if="loading" />

    <!-- No Results -->
    <div
      v-else-if="searchResults?.data?.length === 0"
      class="py-12 text-center"
    >
      <Icon
        name="lucide:search-x"
        class="mx-auto mb-4 h-16 w-16 text-gray-400"
      />
      <h3 class="mb-2 text-lg font-medium text-gray-900">
        ไม่พบผลการค้นหา
      </h3>
      <p class="text-gray-600">
        ลองค้นหาด้วยคำอื่น หรือเลือกจากตัวกรองด่วนด้านบน
      </p>
    </div>

    <!-- Results Grid -->
    <div
      v-else
      class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    >
      <ChordCard
        v-for="chord in searchResults?.data"
        :key="chord.id"
        :chord="chord"
        @click="viewChord(chord.id)"
      />
    </div>

    <!-- Pagination -->
    <div
      v-if="searchResults?.pagination && searchResults.pagination.totalPages > 1"
      class="mt-8"
    >
      <nav class="flex justify-center">
        <div class="flex space-x-2">
          <button
            :disabled="!searchResults.pagination.hasPrev"
            class="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
            @click="changePage(searchResults.pagination.currentPage - 1)"
          >
            ก่อนหน้า
          </button>

          <span class="px-3 py-2 text-sm font-medium text-gray-700">
            หน้า {{ searchResults.pagination.currentPage }} จาก {{ searchResults.pagination.totalPages }}
          </span>

          <button
            :disabled="!searchResults.pagination.hasNext"
            class="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
            @click="changePage(searchResults.pagination.currentPage + 1)"
          >
            ถัดไป
          </button>
        </div>
      </nav>
    </div>
  </div>
</template>

<script setup lang="ts">
// Router
const route = useRoute()
const router = useRouter()

// Reactive data
const searchQuery = ref('')
const currentQuery = ref('')
const loading = ref(false)
const searchResults = ref<any>(null)
const currentPage = ref(1)

const quickFilters = [
  'C',
  'G',
  'Am',
  'F',
  'Em',
  'D',
  'เมเจอร์',
  'ไมเนอร์',
  'ง่าย',
  'ปานกลาง',
  'พื้นฐาน',
  'โอเพ่น',
  'บาร์เร่',
]

// Methods
const performSearch = async () => {
  if (!searchQuery.value.trim() && !currentQuery.value) return

  loading.value = true
  currentQuery.value = searchQuery.value.trim()
  currentPage.value = 1

  try {
    await fetchResults()

    // Update URL
    const query = currentQuery.value
      ? {
        q: currentQuery.value,
      }
      : {}

    await router.push({
      query,
    })
  } catch (error) {
    console.error('Search error:', error)
  } finally {
    loading.value = false
  }
}

const quickSearch = (filter: string) => {
  searchQuery.value = filter
  performSearch()
}

const fetchResults = async () => {
  const endpoint = currentQuery.value
    ? `/api/chords/search?q=${encodeURIComponent(currentQuery.value)}&page=${currentPage.value}&limit=20`
    : `/api/chords?page=${currentPage.value}&limit=20`

  const response = await $fetch(endpoint)

  searchResults.value = response
}

const changePage = async (page: number) => {
  if (page < 1) return

  currentPage.value = page
  loading.value = true

  try {
    await fetchResults()
  } catch (error) {
    console.error('Pagination error:', error)
  } finally {
    loading.value = false
  }
}

const viewChord = (id: string) => {
  navigateTo(`/chord/${id}`)
}

// Initialize from URL query
onMounted(async () => {
  const urlQuery = route.query.q as string

  if (urlQuery) {
    searchQuery.value = urlQuery
    currentQuery.value = urlQuery
  }

  loading.value = true

  try {
    await fetchResults()
  } catch (error) {
    console.error('Initial load error:', error)
  } finally {
    loading.value = false
  }
})
</script>
