export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const searchQuery = (query.q as string) || ''
  const page = Number.parseInt(query.page as string, 10) || 1
  const limit = Number.parseInt(query.limit as string, 10) || 30

  try {
    // เรียก API จาก dochord.com
    const dochordUrl = `https://www.dochord.com/wp-json/wp/v2/posts?search=${encodeURIComponent(searchQuery)}&_embed&per_page=${limit}&page=${page}`

    const response = await $fetch(dochordUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    })

    // แปลงข้อมูลจาก dochord.com ให้เป็นรูปแบบที่เราต้องการ
    const transformedSongs = response.map((post: any) => {
      const title = post.title?.rendered || ''

      // ดึงข้อมูลศิลปินจาก _embedded.wp:term
      const artistInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
        termGroup.some((term: any) => term.taxonomy === 'artist'),
      )?.find((term: any) => term.taxonomy === 'artist')

      // ดึงรูปภาพจาก _embedded.wp:featuredmedia
      const featuredImage = post._embedded?.['wp:featuredmedia']?.[0]?.source_url || ''

      return {
        id: post.id.toString(),
        title: title.replace(/<[^>]*>/g, ''),
        artist: artistInfo?.name || 'Unknown Artist',
        thumbnail: featuredImage,
      }
    })

    const totalCount = Number.parseInt(event.node.res.getHeader('X-WP-Total') as string, 10) || transformedSongs.length

    return {
      count: transformedSongs.length,
      limit: limit,
      page: page,
      total: totalCount,
      items: transformedSongs,
    }
  } catch (error) {
    event.node.res.statusCode = 500
    console.error('Error fetching from dochord.com:', error)

    return {
      code: '500',
      message: 'Failed to fetch',
    }
  }
})

const extractKeyFromContent = (content: string): string => {
  const keyPatterns = [
    /คีย์\s*(?::\s*)?([A-G][#b]?m?)/i,
    /key\s*(?::\s*)?([A-G][#b]?m?)/i,
    /ทำนอง\s*([A-G][#b]?m?)/i,
  ]

  for (const pattern of keyPatterns) {
    const match = content.match(pattern)

    if (match) {
      return match[1]
    }
  }

  return 'C'
}

const extractCapoFromContent = (content: string): number => {
  const capoPatterns = [
    /capo\s*(?::\s*)?(\d+)/i,
    /คาโป\s*(?::\s*)?(\d+)/,
    /เฟรต\s*(\d+)/,
  ]

  for (const pattern of capoPatterns) {
    const match = content.match(pattern)

    if (match) {
      return Number.parseInt(match[1], 10)
    }
  }

  return 0
}

const extractChordsFromContent = (content: string): string[] => {
  const chordPattern = /\b([A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*)\b/g
  const matches = content.match(chordPattern) || []

  const uniqueChords = [...new Set(matches)]
    .filter((chord) => /^[A-G][#b]?(?:m|maj|min|sus|add|dim|aug)?\d*$/.test(chord))
    .slice(0, 10)

  return uniqueChords.length > 0 ? uniqueChords : ['C', 'G', 'Am', 'F']
}

const extractYouTubeId = (content: string): string => {
  const youtubePatterns = [
    /youtube\.com\/watch\?v=([\w-]+)/,
    /youtu\.be\/([\w-]+)/,
    /youtube\.com\/embed\/([\w-]+)/,
  ]

  for (const pattern of youtubePatterns) {
    const match = content.match(pattern)

    if (match) {
      return match[1]
    }
  }

  return ''
}
