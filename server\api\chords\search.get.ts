export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const searchQuery = (query.q as string) || ''
  const page = Number.parseInt(query.page as string, 10) || 1
  const limit = Number.parseInt(query.limit as string, 10) || 20

  // Mock chord data สำหรับการค้นหา
  const allChords = [
    {
      id: '1',
      name: 'C',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [0, 1, 0, 2, 3, 0],
        fingers: [0, 1, 0, 2, 3, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [3, 3, 2, 0, 1, 0],
          fingers: [3, 4, 2, 0, 1, 0],
          baseFret: 1,
        },
      ],
      notes: ['C', 'E', 'G'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'เมเจอร์'],
      relatedSongs: ['Let It Be - The Beatles', 'Wonderwall - Oasis'],
      description: 'คอร์ด C เมเจอร์ เป็นคอร์ดพื้นฐานที่ง่ายที่สุดสำหรับผู้เริ่มต้น',
    },
    {
      id: '2',
      name: '<PERSON>',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [3, 2, 0, 0, 3, 3],
        fingers: [3, 1, 0, 0, 4, 4],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [3, 2, 0, 0, 0, 3],
          fingers: [3, 1, 0, 0, 0, 4],
          baseFret: 1,
        },
      ],
      notes: ['G', 'B', 'D'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'เมเจอร์'],
      relatedSongs: ['Good Riddance - Green Day', 'Wish You Were Here - Pink Floyd'],
      description: 'คอร์ด G เมเจอร์ เป็นคอร์ดที่ใช้บ่อยในเพลงป็อป',
    },
    {
      id: '3',
      name: 'Am',
      type: 'minor',
      typeThai: 'ไมเนอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [0, 0, 2, 2, 1, 0],
        fingers: [0, 0, 2, 3, 1, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [5, 7, 7, 5, 5, 5],
          fingers: [1, 3, 4, 1, 1, 1],
          baseFret: 5,
        },
      ],
      notes: ['A', 'C', 'E'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'ไมเนอร์'],
      relatedSongs: ['Stairway to Heaven - Led Zeppelin', 'House of the Rising Sun - The Animals'],
      description: 'คอร์ด A ไมเนอร์ ให้เสียงที่เศร้าและอ่อนโยน',
    },
    {
      id: '4',
      name: 'F',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'intermediate',
      difficultyThai: 'ปานกลาง',
      fingering: {
        frets: [1, 1, 3, 3, 2, 1],
        fingers: [1, 1, 4, 3, 2, 1],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [1, 3, 3, 2, 1, 1],
          fingers: [1, 3, 4, 2, 1, 1],
          baseFret: 1,
        },
      ],
      notes: ['F', 'A', 'C'],
      tags: ['บาร์เร่', 'เมเจอร์', 'ปานกลาง'],
      relatedSongs: ['Hey Jude - The Beatles', 'Blackbird - The Beatles'],
      description: 'คอร์ด F เมเจอร์ เป็นคอร์ดบาร์เร่ที่ต้องใช้นิ้วกดหลายเส้น',
    },
    {
      id: '5',
      name: 'Em',
      type: 'minor',
      typeThai: 'ไมเนอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [0, 2, 2, 0, 0, 0],
        fingers: [0, 2, 3, 0, 0, 0],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [0, 2, 2, 0, 3, 0],
          fingers: [0, 1, 2, 0, 4, 0],
          baseFret: 1,
        },
      ],
      notes: ['E', 'G', 'B'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'ไมเนอร์'],
      relatedSongs: ['Losing Religion - R.E.M.', 'Creep - Radiohead'],
      description: 'คอร์ด E ไมเนอร์ ง่ายที่สุดในกลุ่มไมเนอร์',
    },
    {
      id: '6',
      name: 'D',
      type: 'major',
      typeThai: 'เมเจอร์',
      difficulty: 'beginner',
      difficultyThai: 'ง่าย',
      fingering: {
        frets: [-1, -1, 0, 2, 3, 2],
        fingers: [0, 0, 0, 1, 3, 2],
        baseFret: 1,
      },
      alternatives: [
        {
          frets: [2, 0, 0, 2, 3, 2],
          fingers: [1, 0, 0, 2, 4, 3],
          baseFret: 1,
        },
      ],
      notes: ['D', 'F#', 'A'],
      tags: ['พื้นฐาน', 'โอเพ่น', 'เมเจอร์'],
      relatedSongs: ['Wonderwall - Oasis', 'Free Fallin\' - Tom Petty'],
      description: 'คอร์ด D เมเจอร์ ให้เสียงที่สดใสและร่าเริง',
    },
  ]

  // ค้นหาคอร์ดตาม query
  let filteredChords = allChords

  if (searchQuery) {
    const lowerQuery = searchQuery.toLowerCase()

    filteredChords = allChords.filter((chord) =>
      chord.name.toLowerCase().includes(lowerQuery)
      || chord.type.toLowerCase().includes(lowerQuery)
      || chord.typeThai.includes(lowerQuery)
      || chord.difficulty.toLowerCase().includes(lowerQuery)
      || chord.difficultyThai.includes(lowerQuery)
      || chord.tags.some((tag) => tag.toLowerCase().includes(lowerQuery))
      || chord.relatedSongs.some((song) => song.toLowerCase().includes(lowerQuery))
      || chord.description.includes(lowerQuery),
    )
  }

  // Pagination logic
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit
  const paginatedChords = filteredChords.slice(startIndex, endIndex)

  const totalCount = filteredChords.length
  const totalPages = Math.ceil(totalCount / limit)

  return {
    success: true,
    query: searchQuery,
    data: paginatedChords,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  }
})
