<template>
  <div
    v-if="isVisible"
    class="bg-primary-50 fixed right-0 bottom-0 left-0 z-50 border-gray-200 shadow-lg"
  >
    <!-- Player Container -->
    <div class="flex items-center justify-between px-4 py-3">
      <!-- Song Info -->
      <div class="flex min-w-0 flex-1 items-center space-x-3">
        <div
          class="h-12 w-12 flex-shrink-0 cursor-pointer overflow-hidden rounded-md bg-gray-200"
          @click="toggleExpanded"
        >
          <Image
            class="w-12 rounded-md object-cover"
            :src="currentSong?.thumbnail"
            :alt="currentSong?.title || 'ไม่มีชื่อเพลง' "
          >
            <template #error>
              <div class="flex h-12 w-12 items-center justify-center rounded-md bg-white">
                <Icon
                  name="ph:music-notes"
                  class="size-8"
                />
              </div>
            </template>
          </Image>
        </div>
        <div class="min-w-0 flex-1">
          <h4 class="truncate text-sm font-medium text-gray-900">
            {{ currentSong?.title || 'ไม่มีชื่อเพลง' }}
          </h4>
          <p class="truncate text-xs text-gray-500">
            {{ currentSong?.artist || 'ไม่ทราบศิลปิน' }}
          </p>
        </div>
      </div>

      <!-- Player Controls -->
      <div class="flex flex-shrink-0 items-center space-x-4">
        <!-- Play/Pause Button -->
        <Button
          square
          class="rounded-full"
          :icon="isPlaying ? 'ph:pause' : 'ph:play'"
          @click="togglePlayPause"
        />
      </div>

      <!-- Volume and Actions -->
      <div class="ml-4 flex flex-shrink-0 items-center space-x-3">
        <!-- Progress Time -->
        <div class="hidden items-center space-x-2 text-xs text-gray-500 sm:flex">
          <span>{{ formatTime(currentTime) }}</span>
          <span>/</span>
          <span>{{ formatTime(duration) }}</span>
        </div>

        <!-- Volume Control -->
        <div class="hidden items-center space-x-2 md:flex">
          <button
            class="p-1 text-gray-600 transition-colors hover:text-gray-900"
            @click="toggleMute"
          >
            <Icon
              v-if="isMuted || volume === 0"
              name="lucide:volume-x"
              class="h-4 w-4"
            />
            <Icon
              v-else-if="volume < 50"
              name="lucide:volume-1"
              class="h-4 w-4"
            />
            <Icon
              v-else
              name="lucide:volume-2"
              class="h-4 w-4"
            />
          </button>
          <input
            v-model="volume"
            type="range"
            min="0"
            max="100"
            class="h-1 w-20 cursor-pointer appearance-none rounded-lg bg-white"
            @input="updateVolume"
          />
        </div>

        <!-- Expand/Collapse Button -->
        <button
          class="hidden p-2 text-gray-600 transition-colors hover:text-gray-900"
          @click="toggleExpanded"
        >
          <Icon
            v-if="isExpanded"
            name="lucide:chevron-down"
            class="h-5 w-5"
          />
          <Icon
            v-else
            name="lucide:chevron-up"
            class="h-5 w-5"
          />
        </button>

        <!-- Close Button -->
        <button
          class="p-2 text-gray-600 transition-colors hover:text-gray-900"
          @click="closePlayer"
        >
          <Icon
            name="lucide:x"
            class="h-5 w-5"
          />
        </button>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="px-4 pb-2">
      <div class="relative">
        <div class="h-1 w-full rounded-full bg-white">
          <div
            class="bg-primary h-1 rounded-full transition-all duration-300"
            :style="{ width: `${progressPercentage}%` }"
          />
        </div>
        <input
          v-model="seekPosition"
          type="range"
          min="0"
          max="100"
          class="accent-primary absolute top-0 left-0 h-1 w-full cursor-pointer opacity-0"
          @input="seekTo"
        />
      </div>
    </div>

    <!-- Expanded Video Player -->
    <div
      v-if="isExpanded"
      class="border-t border-gray-200 bg-gray-50 p-4"
    >
      <div class="mx-auto max-w-md">
        <div class="aspect-video overflow-hidden rounded-lg bg-black">
          <div
            ref="playerContainer"
            class="h-full w-full"
          />
        </div>
      </div>
    </div>

    <!-- Hidden YouTube Player (for audio-only mode) -->
    <div
      v-show="!isExpanded"
      class="absolute -top-96 left-0 h-1 w-1 overflow-hidden"
    >
      <div
        ref="hiddenPlayerContainer"
        class="h-full w-full"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// Global player state
const {
  currentSong,
  isVisible,
  isPlaying,
  playNext,
  playPrevious,
  closePlayer: closeGlobalPlayer,
  setPlaying,
  hasNext,
  hasPrevious,
} = useYouTubePlayer()

// Local player state
const isExpanded = ref(false)
const isMuted = ref(false)
const volume = ref(50)
const currentTime = ref(0)
const duration = ref(0)
const seekPosition = ref(0)

// YouTube Player
const playerContainer = ref<HTMLElement>()
const hiddenPlayerContainer = ref<HTMLElement>()
let player: any = null
let playerReady = false

// Computed
const progressPercentage = computed(() => {
  if (duration.value === 0) return 0

  return (currentTime.value / duration.value) * 100
})

// Methods
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)

  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const loadYouTubeAPI = (): Promise<void> => {
  return new Promise((resolve) => {
    if (window.YT && window.YT.Player) {
      resolve()

      return
    }

    const script = document.createElement('script')

    script.src = 'https://www.youtube.com/iframe_api'
    document.head.appendChild(script)

    window.onYouTubeIframeAPIReady = () => {
      resolve()
    }
  })
}

const initializePlayer = async (youtubeId: string) => {
  await loadYouTubeAPI()

  const container = isExpanded.value ? playerContainer.value : hiddenPlayerContainer.value

  if (!container) return

  player = new window.YT.Player(container, {
    height: isExpanded.value ? '100%' : '1',
    width: isExpanded.value ? '100%' : '1',
    videoId: youtubeId,
    playerVars: {
      autoplay: 0,
      controls: 0,
      disablekb: 1,
      fs: 0,
      iv_load_policy: 3,
      modestbranding: 1,
      playsinline: 1,
      rel: 0,
    },
    events: {
      onReady: onPlayerReady,
      onStateChange: onPlayerStateChange,
    },
  })
}

const onPlayerReady = () => {
  playerReady = true
  duration.value = player.getDuration()
  updateVolume()
}

const onPlayerStateChange = (event: any) => {
  const state = event.data
  const playing = state === window.YT.PlayerState.PLAYING

  setPlaying(playing)

  if (playing) {
    startTimeUpdate()
  } else {
    stopTimeUpdate()
  }
}

let timeUpdateInterval: NodeJS.Timeout | null = null

const startTimeUpdate = () => {
  if (timeUpdateInterval) return

  timeUpdateInterval = setInterval(() => {
    if (player && playerReady) {
      currentTime.value = player.getCurrentTime()
      seekPosition.value = progressPercentage.value
    }
  }, 1000)
}

const stopTimeUpdate = () => {
  if (timeUpdateInterval) {
    clearInterval(timeUpdateInterval)
    timeUpdateInterval = null
  }
}

const togglePlayPause = () => {
  if (!player || !playerReady) return

  if (isPlaying.value) {
    player.pauseVideo()
  } else {
    player.playVideo()
  }
}

const updateVolume = () => {
  if (!player || !playerReady) return
  player.setVolume(isMuted.value ? 0 : volume.value)
}

const toggleMute = () => {
  isMuted.value = !isMuted.value
  updateVolume()
}

const seekTo = () => {
  if (!player || !playerReady) return
  const seekTime = (seekPosition.value / 100) * duration.value

  player.seekTo(seekTime)
}

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value

  // Re-initialize player in new container
  if (currentSong.value?.youtubeId) {
    nextTick(() => {
      initializePlayer(currentSong.value!.youtubeId)
    })
  }
}

const closePlayer = () => {
  if (player) {
    player.destroy()
    player = null
  }

  stopTimeUpdate()
  closeGlobalPlayer()
}

const previousTrack = () => {
  if (hasPrevious.value) {
    playPrevious()
  }
}

const nextTrack = () => {
  if (hasNext.value) {
    playNext()
  }
}

// Watch for current song changes
watch(currentSong, async (newSong) => {
  if (newSong && newSong.youtubeId) {
    await nextTick()
    await initializePlayer(newSong.youtubeId)
  }
}, {
  immediate: true,
})

// Cleanup
onUnmounted(() => {
  if (player) {
    player.destroy()
  }

  stopTimeUpdate()
})

// Expose methods for parent components
defineExpose({
  closePlayer,
})
</script>

<style scoped>
/* Custom range slider styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #2563eb;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #2563eb;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
